# PowerShell Script - Execute Python Crawler
# For Windows Task Scheduler

# Set error handling
$ErrorActionPreference = "Stop"

# Get script directory
$ScriptDir = Split-Path -Parent $MyInvocation.MyCommand.Path
Set-Location $ScriptDir

Write-Host "===========================================" -ForegroundColor Green
Write-Host "PowerShell Execute Python Crawler Script" -ForegroundColor Green
Write-Host "Current Time: $(Get-Date)" -ForegroundColor Yellow
Write-Host "Current Directory: $PWD" -ForegroundColor Yellow
Write-Host "User Environment: $env:USERNAME" -ForegroundColor Yellow
Write-Host "===========================================" -ForegroundColor Green

try {
    # Check Python availability
    Write-Host "Checking Python environment..." -ForegroundColor Cyan

    $PythonCmd = $null

    # Method 1: Check python in PATH
    try {
        $null = python --version 2>$null
        $PythonCmd = "python"
        Write-Host "Found Python in PATH" -ForegroundColor Green
    }
    catch {
        # Method 2: Try full path
        $PythonPath = "D:\Program Files\Python\python.exe"
        if (Test-Path $PythonPath) {
            try {
                $null = & $PythonPath --version 2>$null
                $PythonCmd = $PythonPath
                Write-Host "Found Python: $PythonPath" -ForegroundColor Green
            }
            catch {
                throw "Python executable cannot run"
            }
        }
        else {
            throw "Cannot find Python executable"
        }
    }

    # Check if send_email.py exists
    $PythonScript = Join-Path $ScriptDir "send_email.py"
    if (-not (Test-Path $PythonScript)) {
        throw "Cannot find Python script: $PythonScript"
    }

    Write-Host "Using Python: $PythonCmd" -ForegroundColor Cyan
    Write-Host "Execute Script: $PythonScript" -ForegroundColor Cyan
    Write-Host "Starting execution..." -ForegroundColor Yellow
    Write-Host "===========================================" -ForegroundColor Green

    # Set environment variables
    $env:PYTHONIOENCODING = "utf-8"

    # Execute Python script
    if ($PythonCmd -eq "python") {
        $Process = Start-Process -FilePath "python" -ArgumentList $PythonScript -Wait -PassThru -NoNewWindow
    }
    else {
        $Process = Start-Process -FilePath $PythonCmd -ArgumentList $PythonScript -Wait -PassThru -NoNewWindow
    }

    # Check execution result
    if ($Process.ExitCode -eq 0) {
        Write-Host "===========================================" -ForegroundColor Green
        Write-Host "Python script executed successfully" -ForegroundColor Green
        Write-Host "Completion Time: $(Get-Date)" -ForegroundColor Yellow
        Write-Host "===========================================" -ForegroundColor Green
        exit 0
    }
    else {
        Write-Host "===========================================" -ForegroundColor Red
        Write-Host "Python script execution failed (Exit Code: $($Process.ExitCode))" -ForegroundColor Red
        Write-Host "===========================================" -ForegroundColor Red
        exit $Process.ExitCode
    }
}
catch {
    Write-Host "===========================================" -ForegroundColor Red
    Write-Host "Error occurred during execution:" -ForegroundColor Red
    Write-Host $_.Exception.Message -ForegroundColor Red
    Write-Host "===========================================" -ForegroundColor Red
    exit 1
}
