# PowerShell脚本 - 执行Python爬虫程序
# 适用于Windows任务计划程序

# 设置错误处理
$ErrorActionPreference = "Stop"

# 获取脚本所在目录
$ScriptDir = Split-Path -Parent $MyInvocation.MyCommand.Path
Set-Location $ScriptDir

Write-Host "===========================================" -ForegroundColor Green
Write-Host "PowerShell执行Python爬虫脚本" -ForegroundColor Green
Write-Host "当前时间: $(Get-Date)" -ForegroundColor Yellow
Write-Host "当前目录: $PWD" -ForegroundColor Yellow
Write-Host "用户环境: $env:USERNAME" -ForegroundColor Yellow
Write-Host "===========================================" -ForegroundColor Green

try {
    # 检查Python是否可用
    Write-Host "检查Python环境..." -ForegroundColor Cyan
    
    $PythonCmd = $null
    
    # 方法1: 检查PATH中的python
    try {
        $null = python --version 2>$null
        $PythonCmd = "python"
        Write-Host "✓ 找到Python在PATH中" -ForegroundColor Green
    }
    catch {
        # 方法2: 尝试完整路径
        $PythonPath = "D:\Program Files\Python\python.exe"
        if (Test-Path $PythonPath) {
            try {
                $null = & $PythonPath --version 2>$null
                $PythonCmd = $PythonPath
                Write-Host "✓ 找到Python: $PythonPath" -ForegroundColor Green
            }
            catch {
                throw "Python可执行文件无法运行"
            }
        }
        else {
            throw "找不到Python可执行文件"
        }
    }
    
    # 检查send_email.py是否存在
    $PythonScript = Join-Path $ScriptDir "send_email.py"
    if (-not (Test-Path $PythonScript)) {
        throw "找不到Python脚本: $PythonScript"
    }
    
    Write-Host "使用Python: $PythonCmd" -ForegroundColor Cyan
    Write-Host "执行脚本: $PythonScript" -ForegroundColor Cyan
    Write-Host "开始执行..." -ForegroundColor Yellow
    Write-Host "===========================================" -ForegroundColor Green
    
    # 设置环境变量
    $env:PYTHONIOENCODING = "utf-8"
    
    # 执行Python脚本
    if ($PythonCmd -eq "python") {
        $Process = Start-Process -FilePath "python" -ArgumentList $PythonScript -Wait -PassThru -NoNewWindow
    }
    else {
        $Process = Start-Process -FilePath $PythonCmd -ArgumentList $PythonScript -Wait -PassThru -NoNewWindow
    }
    
    # 检查执行结果
    if ($Process.ExitCode -eq 0) {
        Write-Host "===========================================" -ForegroundColor Green
        Write-Host "✓ Python脚本执行成功" -ForegroundColor Green
        Write-Host "完成时间: $(Get-Date)" -ForegroundColor Yellow
        Write-Host "===========================================" -ForegroundColor Green
        exit 0
    }
    else {
        Write-Host "===========================================" -ForegroundColor Red
        Write-Host "✗ Python脚本执行失败 (退出码: $($Process.ExitCode))" -ForegroundColor Red
        Write-Host "===========================================" -ForegroundColor Red
        exit $Process.ExitCode
    }
}
catch {
    Write-Host "===========================================" -ForegroundColor Red
    Write-Host "✗ 执行过程中发生错误:" -ForegroundColor Red
    Write-Host $_.Exception.Message -ForegroundColor Red
    Write-Host "===========================================" -ForegroundColor Red
    exit 1
}
